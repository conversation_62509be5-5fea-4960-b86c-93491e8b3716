# نظام التحديث المستمر لمواقيت الصلاة

## نظرة عامة

تم إضافة نظام التحديث المستمر لمواقيت الصلاة الذي يعمل بدون الحاجة لاتصال بالإنترنت. يقوم النظام بتحديث مواقيت الصلاة تلقائياً كل دقيقة للحفاظ على دقة المواقيت والعد التنازلي.

## الميزات الجديدة

### 1. التحديث التلقائي كل دقيقة
- يتم تحديث مواقيت الصلاة تلقائياً كل دقيقة
- يعمل بدون الحاجة لاتصال بالإنترنت
- يحافظ على دقة العد التنازلي للصلاة القادمة

### 2. إعدادات قابلة للتخصيص
- إمكانية تفعيل/تعطيل التحديث المستمر
- تغيير تكرار التحديث (30 ثانية، دقيقة، دقيقتان، 5 دقائق)
- التحكم في عرض سجل التحديثات
- التحكم في تحديث العرض والعد التنازلي

### 3. واجهة مستخدم متقدمة
- قسم مخصص في الإعدادات للتحكم في التحديث المستمر
- عرض حالة النظام (يعمل/متوقف)
- عرض آخر وقت تحديث
- أزرار للاختبار والتحكم

## الملفات الجديدة

### 1. `prayer-times-continuous-updater.js`
الملف الرئيسي لنظام التحديث المستمر:
- إدارة المؤقتات والتحديث التلقائي
- حفظ وتحميل الإعدادات
- إطلاق الأحداث المخصصة
- التعامل مع تغيير المدينة والإعدادات

### 2. `continuous-update-ui.js`
واجهة المستخدم لإدارة التحديث المستمر:
- ربط عناصر HTML بالوظائف
- إدارة الإعدادات من واجهة المستخدم
- عرض الحالة والإشعارات
- التحكم في تشغيل وإيقاف النظام

### 3. `CONTINUOUS-UPDATE-README.md`
ملف التوثيق هذا

## التحديثات على الملفات الموجودة

### 1. `prayer-times-manager.js`
- إضافة إعداد `continuousUpdateEnabled` للإعدادات الافتراضية
- إضافة دوال `setupContinuousUpdate()` و `stopContinuousUpdate()`
- إضافة دالة `toggleContinuousUpdate()` للتحكم
- تحديث دالة `initialize()` لتشمل التحديث المستمر

### 2. `prayer-times-init.js`
- إضافة مستمع للحدث `prayerTimesContinuousUpdate`
- إضافة دالة `updateNextPrayerCountdown()`
- تحسين تحديث العرض والعد التنازلي

### 3. `index.html`
- إضافة قسم "إعدادات التحديث المستمر" في قائمة الإعدادات
- إضافة عناصر التحكم (checkboxes, select, buttons)
- إضافة عرض حالة النظام
- ربط الملفات الجديدة

## كيفية الاستخدام

### 1. التفعيل التلقائي
النظام مفعل افتراضياً ويبدأ العمل تلقائياً عند تحميل الصفحة.

### 2. التحكم اليدوي
1. افتح قائمة الإعدادات (⚙️)
2. ابحث عن قسم "إعدادات التحديث المستمر"
3. استخدم الخيارات المتاحة:
   - ✅ تفعيل/تعطيل التحديث المستمر
   - 🕐 تغيير تكرار التحديث
   - 📝 تفعيل/تعطيل سجل التحديثات
   - 🖥️ تفعيل/تعطيل تحديث العرض
   - ⏰ تفعيل/تعطيل تحديث العد التنازلي

### 3. الأزرار المتاحة
- **حفظ إعدادات التحديث المستمر**: حفظ جميع الإعدادات
- **اختبار التحديث المستمر**: تنفيذ تحديث فوري للاختبار
- **إيقاف التحديث المستمر**: إيقاف النظام مؤقتاً

## الأحداث المخصصة

### 1. `continuousUpdateStarted`
يتم إطلاقه عند بدء التحديث المستمر

### 2. `continuousUpdateStopped`
يتم إطلاقه عند إيقاف التحديث المستمر

### 3. `prayerTimesContinuousUpdate`
يتم إطلاقه عند كل تحديث مع البيانات التالية:
```javascript
{
    city: 'Asia/Amman',
    times: { fajr: '04:30', dhuhr: '12:15', ... },
    timestamp: Date
}
```

## التخزين المحلي

### الإعدادات المحفوظة:
- `continuousUpdaterSettings`: إعدادات نظام التحديث المستمر
- `continuousUpdateUISettings`: إعدادات واجهة المستخدم

## المتطلبات

- متصفح حديث يدعم JavaScript ES6
- لا يتطلب اتصال بالإنترنت للعمل
- يعمل مع جميع أنظمة التشغيل

## الفوائد

### 1. دقة عالية
- تحديث مستمر يضمن دقة المواقيت
- عد تنازلي دقيق للصلاة القادمة
- تحديث تلقائي عند تغيير اليوم

### 2. عمل بدون إنترنت
- حساب محلي لمواقيت الصلاة
- لا يعتمد على خدمات خارجية
- يعمل في البيئات المنقطعة عن الإنترنت

### 3. قابلية التخصيص
- إعدادات متنوعة للتحكم في السلوك
- واجهة مستخدم سهلة الاستخدام
- إمكانية التحكم الكامل في النظام

### 4. الأداء المحسن
- استهلاك منخفض للموارد
- تحديث ذكي يتجنب العمليات غير الضرورية
- إدارة فعالة للذاكرة

## استكشاف الأخطاء

### 1. النظام لا يعمل
- تأكد من تفعيل JavaScript في المتصفح
- تحقق من وحدة التحكم للأخطاء
- تأكد من تحميل جميع الملفات بشكل صحيح

### 2. التحديث لا يحدث
- تحقق من إعدادات التحديث المستمر
- تأكد من أن النظام مفعل
- راجع سجل وحدة التحكم

### 3. مشاكل في الأداء
- قلل تكرار التحديث إلى دقيقتين أو أكثر
- عطل سجل التحديثات إذا لم تكن بحاجة إليه
- أعد تشغيل المتصفح

## الدعم والتطوير

هذا النظام قابل للتوسع ويمكن تطويره لإضافة ميزات جديدة مثل:
- تحديث مواقيت متعددة المدن
- تنبيهات مخصصة
- تكامل مع أنظمة أخرى
- واجهة برمجة تطبيقات (API)

## الخلاصة

نظام التحديث المستمر يوفر تجربة مستخدم محسنة مع دقة عالية في مواقيت الصلاة، ويعمل بشكل موثوق بدون الحاجة لاتصال بالإنترنت، مما يجعله مثالياً للاستخدام في المساجد والبيئات المختلفة.
