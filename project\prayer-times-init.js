/**
 * ملف تهيئة نظام مواقيت الصلاة المحسن
 * يقوم بتهيئة نظام مواقيت الصلاة وإعداد التحديث التلقائي اليومي
 */

// تهيئة نظام مواقيت الصلاة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('بدء تهيئة نظام مواقيت الصلاة المحسن...');
    
    // التحقق من وجود كائن PrayerTimesManager
    if (typeof PrayerTimesManager !== 'undefined') {
        // تهيئة نظام مواقيت الصلاة
        PrayerTimesManager.initialize();
        
        console.log('تم تهيئة نظام مواقيت الصلاة المحسن بنجاح');
        
        // إضافة مستمع حدث للتحديث اليومي
        window.addEventListener('prayerTimesUpdated', function() {
            console.log('تم استلام حدث تحديث مواقيت الصلاة اليومي في ملف التهيئة');

            // تحديث عرض مواقيت الصلاة
            updatePrayerTimesDisplay();
        });

        // إضافة مستمع حدث للتحديث المستمر كل دقيقة
        window.addEventListener('prayerTimesContinuousUpdate', function(event) {
            console.log('تم استلام حدث التحديث المستمر لمواقيت الصلاة:', event.detail);

            // تحديث عرض مواقيت الصلاة
            updatePrayerTimesDisplay();

            // تحديث العد التنازلي للصلاة القادمة
            updateNextPrayerCountdown();
        });
        
        // دالة لتحديث عرض مواقيت الصلاة
        function updatePrayerTimesDisplay() {
            try {
                // الحصول على المدينة الحالية
                const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                
                // الحصول على التاريخ الحالي
                const currentDate = new Date();
                
                // الحصول على مواقيت الصلاة المحدثة
                const updatedTimes = PrayerTimesManager.getPrayerTimes(currentCity, currentDate);
                
                if (!updatedTimes) {
                    console.error('فشل في الحصول على مواقيت الصلاة المحدثة');
                    return;
                }
                
                console.log('مواقيت الصلاة المحدثة:', updatedTimes);
                
                // تحديث عرض مواقيت الصلاة في واجهة المستخدم
                if (typeof window.updatePrayerTimes === 'function') {
                    window.updatePrayerTimes();
                    console.log('تم تحديث عرض مواقيت الصلاة باستخدام updatePrayerTimes');
                } else if (typeof window.updateAllPrayerTimesDisplays === 'function') {
                    window.updateAllPrayerTimesDisplays();
                    console.log('تم تحديث عرض مواقيت الصلاة باستخدام updateAllPrayerTimesDisplays');
                } else {
                    console.warn('لم يتم العثور على دالة لتحديث عرض مواقيت الصلاة');
                    
                    // محاولة تحديث عناصر العرض مباشرة
                    updatePrayerTimeElements(updatedTimes);
                }
            } catch (error) {
                console.error('خطأ في تحديث عرض مواقيت الصلاة:', error);
            }
        }
        
        // دالة لتحديث عناصر عرض مواقيت الصلاة مباشرة
        function updatePrayerTimeElements(times) {
            try {
                // تحديث عناصر عرض مواقيت الصلاة
                const prayers = ['fajr', 'sunrise', 'dhuhr', 'asr', 'maghrib', 'isha'];

                prayers.forEach(prayer => {
                    const element = document.getElementById(`${prayer}-time-display`);
                    if (element && times[prayer]) {
                        element.textContent = times[prayer];
                    }
                });

                console.log('تم تحديث عناصر عرض مواقيت الصلاة مباشرة');
            } catch (error) {
                console.error('خطأ في تحديث عناصر عرض مواقيت الصلاة:', error);
            }
        }

        // دالة لتحديث العد التنازلي للصلاة القادمة
        function updateNextPrayerCountdown() {
            try {
                // الحصول على المدينة الحالية
                const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';

                // الحصول على الصلاة القادمة
                const nextPrayer = PrayerTimesManager.getNextPrayer(currentCity);

                if (nextPrayer) {
                    // تحديث عرض الصلاة القادمة إذا كانت الدالة متوفرة
                    if (typeof window.updateNextPrayerDisplay === 'function') {
                        window.updateNextPrayerDisplay(nextPrayer);
                    }

                    // تحديث العد التنازلي إذا كانت الدالة متوفرة
                    if (typeof window.updateCountdown === 'function') {
                        window.updateCountdown();
                    }

                    console.log('تم تحديث العد التنازلي للصلاة القادمة:', nextPrayer.arabicName);
                }
            } catch (error) {
                console.error('خطأ في تحديث العد التنازلي للصلاة القادمة:', error);
            }
        }
    } else {
        console.error('كائن PrayerTimesManager غير متوفر');
    }
});
