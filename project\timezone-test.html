<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تزامن المناطق الزمنية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        select, button {
            padding: 8px 12px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .clock-display {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin: 15px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .prayer-times {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .prayer-time {
            text-align: center;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
        }
        .prayer-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .prayer-value {
            font-size: 18px;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار تزامن المناطق الزمنية</h1>
        
        <div class="test-section">
            <h3>اختيار المدينة</h3>
            <select id="city-test-select">
                <option value="Asia/Amman">عمان، الأردن</option>
                <option value="Asia/Riyadh">الرياض، السعودية</option>
                <option value="Asia/Dubai">دبي، الإمارات</option>
                <option value="Africa/Cairo">القاهرة، مصر</option>
                <option value="Asia/Jerusalem">القدس، فلسطين</option>
                <option value="Asia/Baghdad">بغداد، العراق</option>
                <option value="Asia/Kuwait">الكويت</option>
                <option value="Asia/Beirut">بيروت، لبنان</option>
            </select>
            <button onclick="testCityChange()">تغيير المدينة</button>
        </div>

        <div class="test-section">
            <h3>حالة النظام</h3>
            <div id="status-display" class="status">جاري التحميل...</div>
        </div>

        <div class="test-section">
            <h3>الساعة الحالية</h3>
            <div id="clock-display" class="clock-display">--:--:--</div>
            <div id="date-display" style="text-align: center; margin-top: 10px;">--</div>
        </div>

        <div class="test-section">
            <h3>مواقيت الصلاة</h3>
            <div class="prayer-times">
                <div class="prayer-time">
                    <div class="prayer-name">الفجر</div>
                    <div class="prayer-value" id="fajr-time">--:--</div>
                </div>
                <div class="prayer-time">
                    <div class="prayer-name">الشروق</div>
                    <div class="prayer-value" id="sunrise-time">--:--</div>
                </div>
                <div class="prayer-time">
                    <div class="prayer-name">الظهر</div>
                    <div class="prayer-value" id="dhuhr-time">--:--</div>
                </div>
                <div class="prayer-time">
                    <div class="prayer-name">العصر</div>
                    <div class="prayer-value" id="asr-time">--:--</div>
                </div>
                <div class="prayer-time">
                    <div class="prayer-name">المغرب</div>
                    <div class="prayer-value" id="maghrib-time">--:--</div>
                </div>
                <div class="prayer-time">
                    <div class="prayer-name">العشاء</div>
                    <div class="prayer-value" id="isha-time">--:--</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>أدوات الاختبار</h3>
            <button onclick="testTimezoneSync()">اختبار تزامن المناطق الزمنية</button>
            <button onclick="testClockUpdate()">اختبار تحديث الساعة</button>
            <button onclick="testPrayerTimesUpdate()">اختبار تحديث مواقيت الصلاة</button>
            <button onclick="showSystemStatus()">عرض حالة النظام</button>
        </div>

        <div class="test-section">
            <h3>سجل الأحداث</h3>
            <div id="event-log" style="height: 200px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px;"></div>
        </div>
    </div>

    <!-- تضمين الملفات المطلوبة -->
    <script src="cities-database.js"></script>
    <script src="prayer-times-accurate.js"></script>
    <script src="prayer-times-manager.js"></script>
    <script src="clock-new.js"></script>
    <script src="timezone-sync.js"></script>

    <script>
        // متغيرات الاختبار
        let eventLog = [];

        // دالة لإضافة رسالة إلى السجل
        function addToLog(message) {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            eventLog.push(`[${timestamp}] ${message}`);
            
            const logElement = document.getElementById('event-log');
            if (logElement) {
                logElement.innerHTML = eventLog.slice(-20).join('<br>');
                logElement.scrollTop = logElement.scrollHeight;
            }
        }

        // دالة لتحديث حالة النظام
        function updateStatus(message, type = 'success') {
            const statusElement = document.getElementById('status-display');
            if (statusElement) {
                statusElement.textContent = message;
                statusElement.className = `status ${type}`;
            }
            addToLog(`حالة النظام: ${message}`);
        }

        // دالة لتحديث عرض الساعة
        function updateClockDisplay() {
            try {
                const now = new Date();
                const timeString = now.toLocaleTimeString('ar-SA');
                const dateString = now.toLocaleDateString('ar-SA');
                
                document.getElementById('clock-display').textContent = timeString;
                document.getElementById('date-display').textContent = dateString;
            } catch (error) {
                addToLog(`خطأ في تحديث عرض الساعة: ${error.message}`);
            }
        }

        // دالة لتحديث عرض مواقيت الصلاة
        function updatePrayerTimesDisplay() {
            try {
                const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                
                if (typeof PrayerTimesManager !== 'undefined') {
                    const times = PrayerTimesManager.getPrayerTimes(currentCity);
                    
                    if (times) {
                        document.getElementById('fajr-time').textContent = times.fajr || '--:--';
                        document.getElementById('sunrise-time').textContent = times.sunrise || '--:--';
                        document.getElementById('dhuhr-time').textContent = times.dhuhr || '--:--';
                        document.getElementById('asr-time').textContent = times.asr || '--:--';
                        document.getElementById('maghrib-time').textContent = times.maghrib || '--:--';
                        document.getElementById('isha-time').textContent = times.isha || '--:--';
                        
                        addToLog(`تم تحديث مواقيت الصلاة لمدينة ${currentCity}`);
                    } else {
                        addToLog(`لم يتم العثور على مواقيت الصلاة لمدينة ${currentCity}`);
                    }
                } else {
                    addToLog('نظام مواقيت الصلاة غير متوفر');
                }
            } catch (error) {
                addToLog(`خطأ في تحديث مواقيت الصلاة: ${error.message}`);
            }
        }

        // دوال الاختبار
        function testCityChange() {
            const citySelect = document.getElementById('city-test-select');
            const selectedCity = citySelect.value;
            
            addToLog(`اختبار تغيير المدينة إلى: ${selectedCity}`);
            
            // حفظ المدينة الجديدة
            localStorage.setItem('selectedCity', selectedCity);
            
            // تحديث المنطقة الزمنية
            if (typeof TimezoneSync !== 'undefined') {
                TimezoneSync.updateCity(selectedCity);
                updateStatus(`تم تغيير المدينة إلى ${selectedCity}`, 'success');
            } else {
                updateStatus('نظام تزامن المناطق الزمنية غير متوفر', 'error');
            }
        }

        function testTimezoneSync() {
            addToLog('اختبار تزامن المناطق الزمنية...');
            
            if (typeof TimezoneSync !== 'undefined') {
                const status = TimezoneSync.getStatus();
                addToLog(`المدينة الحالية: ${status.currentCity}`);
                addToLog(`المنطقة الزمنية: ${status.currentTimezone}`);
                addToLog(`حالة التهيئة: ${status.isInitialized ? 'مهيأ' : 'غير مهيأ'}`);
                updateStatus('تم اختبار تزامن المناطق الزمنية بنجاح', 'success');
            } else {
                updateStatus('نظام تزامن المناطق الزمنية غير متوفر', 'error');
            }
        }

        function testClockUpdate() {
            addToLog('اختبار تحديث الساعة...');
            updateClockDisplay();
            updateStatus('تم اختبار تحديث الساعة', 'success');
        }

        function testPrayerTimesUpdate() {
            addToLog('اختبار تحديث مواقيت الصلاة...');
            updatePrayerTimesDisplay();
            updateStatus('تم اختبار تحديث مواقيت الصلاة', 'success');
        }

        function showSystemStatus() {
            addToLog('عرض حالة النظام...');
            
            const systems = [
                { name: 'CITIES_DATABASE', obj: window.CITIES_DATABASE },
                { name: 'PrayerTimesManager', obj: window.PrayerTimesManager },
                { name: 'TimezoneSync', obj: window.TimezoneSync },
                { name: 'updateCurrentTimezone', obj: window.updateCurrentTimezone },
                { name: 'updateAnalogClock', obj: window.updateAnalogClock },
                { name: 'updateDigitalClock', obj: window.updateDigitalClock }
            ];
            
            systems.forEach(system => {
                const status = system.obj ? 'متوفر' : 'غير متوفر';
                addToLog(`${system.name}: ${status}`);
            });
            
            updateStatus('تم عرض حالة النظام', 'success');
        }

        // مستمعات الأحداث
        window.addEventListener('timezoneChanged', function(event) {
            addToLog(`حدث تغيير المنطقة الزمنية: ${event.detail.timezone}`);
            updateClockDisplay();
            updatePrayerTimesDisplay();
        });

        window.addEventListener('cityChanged', function(event) {
            addToLog(`حدث تغيير المدينة: ${event.detail.city}`);
            updateClockDisplay();
            updatePrayerTimesDisplay();
        });

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addToLog('تم تحميل صفحة الاختبار');
            updateStatus('جاري التهيئة...', 'success');
            
            // تحديث دوري للساعة
            setInterval(updateClockDisplay, 1000);
            
            // تحديث أولي
            setTimeout(() => {
                updateClockDisplay();
                updatePrayerTimesDisplay();
                updateStatus('تم تهيئة صفحة الاختبار بنجاح', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
