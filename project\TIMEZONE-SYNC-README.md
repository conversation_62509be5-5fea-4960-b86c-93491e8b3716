# نظام تزامن المناطق الزمنية

## نظرة عامة

تم إضافة نظام تزامن المناطق الزمنية الذي يضمن تطابق الساعة الرقمية والساعة التناظرية مع وقت الدولة/المدينة المختارة، بالإضافة إلى تطابق مواقيت الصلاة مع نفس المنطقة الزمنية.

## الميزات الجديدة

### 1. تزامن الساعات مع المنطقة الزمنية
- **الساعة الرقمية**: تعرض الوقت الحالي للمدينة المختارة
- **الساعة التناظرية**: تتحرك عقاربها حسب وقت المدينة المختارة
- **التاريخ**: يعرض التاريخ الميلادي والهجري حسب المنطقة الزمنية

### 2. تطابق مواقيت الصلاة
- حساب مواقيت الصلاة حسب المنطقة الزمنية للمدينة المختارة
- العد التنازلي للصلاة القادمة يعتمد على وقت المدينة
- تحديث تلقائي عند تغيير المدينة

### 3. واجهة مستخدم محسنة
- عرض المنطقة الزمنية الحالية في قسم الإعدادات
- تحديث فوري عند تغيير المدينة
- مؤشرات بصرية للحالة الحالية

## الملفات الجديدة والمحدثة

### الملفات الجديدة:
1. **`timezone-sync.js`** - نظام تزامن المناطق الزمنية الرئيسي
2. **`TIMEZONE-SYNC-README.md`** - هذا الملف

### الملفات المحدثة:
1. **`clock-new.js`** - إضافة دعم المناطق الزمنية للساعات
2. **`prayer-times-manager.js`** - تحديث حساب المواقيت حسب المنطقة الزمنية
3. **`prayer-times-continuous-updater.js`** - تزامن التحديث المستمر
4. **`continuous-update-ui.js`** - عرض المنطقة الزمنية في الواجهة
5. **`index.html`** - إضافة مؤشر المنطقة الزمنية

## كيفية العمل

### 1. تحديد المنطقة الزمنية
```javascript
// النظام يحدد المنطقة الزمنية تلقائياً من:
// 1. قاعدة بيانات المدن (CITIES_DATABASE)
// 2. المدينة المحفوظة في localStorage
// 3. القيمة الافتراضية: Asia/Amman
```

### 2. تحويل الوقت
```javascript
// الحصول على الوقت حسب المنطقة الزمنية
function getTimeByTimezone(timezone) {
    const now = new Date();
    return new Date(now.toLocaleString("en-US", {timeZone: timezone}));
}
```

### 3. تزامن الأنظمة
- عند تغيير المدينة، يتم تحديث:
  - المنطقة الزمنية الحالية
  - الساعة الرقمية والتناظرية
  - مواقيت الصلاة
  - العد التنازلي للصلاة القادمة

## الاستخدام

### 1. تغيير المدينة
1. افتح قائمة الإعدادات (⚙️)
2. اختر الدولة من القائمة المنسدلة
3. اختر المدينة من القائمة المنسدلة
4. ستتحدث جميع الساعات والمواقيت تلقائياً

### 2. مراقبة الحالة
- في قسم "إعدادات التحديث المستمر"
- يظهر: "المنطقة الزمنية: Asia/Amman"
- يتحدث تلقائياً عند تغيير المدينة

## الأحداث المخصصة

### 1. `cityChanged`
```javascript
window.addEventListener('cityChanged', (event) => {
    console.log('المدينة الجديدة:', event.detail.city);
    console.log('المنطقة الزمنية:', event.detail.timezone);
});
```

### 2. `timezoneChanged`
```javascript
window.addEventListener('timezoneChanged', (event) => {
    console.log('المنطقة الزمنية الجديدة:', event.detail.timezone);
    console.log('مفتاح المدينة:', event.detail.cityKey);
});
```

## قاعدة بيانات المدن

### تنسيق البيانات:
```javascript
'Asia/Amman': {
    name: 'عمان',
    country: 'الأردن',
    latitude: 31.9552,
    longitude: 35.945,
    timezone: 3,           // أو 'Asia/Amman'
    method: 'Jordan'
}
```

### المناطق الزمنية المدعومة:
- **Asia/Amman** - الأردن، فلسطين، لبنان
- **Asia/Riyadh** - السعودية
- **Asia/Dubai** - الإمارات، عُمان
- **Africa/Cairo** - مصر
- **Africa/Tunis** - تونس، الجزائر
- **Europe/London** - المغرب (GMT)
- **Asia/Karachi** - باكستان
- **Asia/Dhaka** - بنغلاديش

## الفوائد

### 1. دقة عالية
- الساعات تعرض الوقت الصحيح للمدينة المختارة
- مواقيت الصلاة محسوبة حسب المنطقة الزمنية الصحيحة
- العد التنازلي دقيق ومتطابق

### 2. سهولة الاستخدام
- تحديث تلقائي عند تغيير المدينة
- لا حاجة لإعدادات إضافية
- واجهة مستخدم واضحة

### 3. مرونة عالية
- دعم لجميع المناطق الزمنية
- إمكانية إضافة مدن جديدة بسهولة
- تكامل مع الأنظمة الموجودة

### 4. موثوقية
- معالجة الأخطاء بشكل صحيح
- قيم افتراضية آمنة
- تسجيل مفصل للأحداث

## مثال عملي

### قبل التحديث:
- الساعة تعرض الوقت المحلي للجهاز
- مواقيت الصلاة قد لا تتطابق مع الوقت المعروض
- عدم تناسق بين الأنظمة المختلفة

### بعد التحديث:
- اختيار "الرياض" من القائمة
- الساعة تعرض وقت الرياض
- مواقيت الصلاة محسوبة لوقت الرياض
- العد التنازلي يعتمد على وقت الرياض
- جميع الأنظمة متزامنة ومتطابقة

## استكشاف الأخطاء

### 1. الساعة لا تتحدث
- تحقق من اختيار المدينة في الإعدادات
- راجع وحدة التحكم للأخطاء
- تأكد من تحميل ملف `timezone-sync.js`

### 2. مواقيت الصلاة غير صحيحة
- تأكد من وجود المدينة في قاعدة البيانات
- تحقق من المنطقة الزمنية المعروضة
- راجع إعدادات طريقة الحساب

### 3. عدم تزامن الأنظمة
- أعد تحميل الصفحة
- تحقق من الأحداث في وحدة التحكم
- تأكد من تفعيل التحديث المستمر

## التطوير المستقبلي

### ميزات مقترحة:
- دعم التوقيت الصيفي التلقائي
- عرض أوقات متعددة المدن
- تنبيهات مخصصة حسب المنطقة الزمنية
- تكامل مع خدمات الطقس المحلية

## الخلاصة

نظام تزامن المناطق الزمنية يوفر تجربة متكاملة ومتطابقة لجميع عناصر التطبيق، مما يضمن دقة عالية وسهولة في الاستخدام، ويجعل التطبيق مناسباً للاستخدام في أي مكان في العالم مع الحفاظ على الدقة والتناسق.
