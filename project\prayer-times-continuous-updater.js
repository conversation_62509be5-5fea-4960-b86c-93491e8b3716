/**
 * نظام التحديث المستمر لمواقيت الصلاة
 * يعمل بدون إنترنت ويحدث المواقيت كل دقيقة
 * نسخة 1.0
 */

const PrayerTimesContinuousUpdater = {
    // متغيرات النظام
    isRunning: false,
    updateInterval: null,
    lastUpdateTime: null,
    updateFrequency: 60000, // كل دقيقة (60000 ميلي ثانية)
    
    // إعدادات التحديث
    settings: {
        enabled: true,
        autoStart: true,
        logUpdates: true,
        updateDisplay: true,
        updateCountdown: true
    },

    // تهيئة النظام
    initialize: function() {
        console.log('تهيئة نظام التحديث المستمر لمواقيت الصلاة...');
        
        // تحميل الإعدادات
        this.loadSettings();
        
        // بدء التحديث التلقائي إذا كان مفعلاً
        if (this.settings.enabled && this.settings.autoStart) {
            this.start();
        }
        
        // إضافة مستمعات الأحداث
        this.setupEventListeners();
        
        console.log('تم تهيئة نظام التحديث المستمر بنجاح');
    },

    // بدء التحديث المستمر
    start: function() {
        try {
            if (this.isRunning) {
                console.log('نظام التحديث المستمر يعمل بالفعل');
                return false;
            }

            console.log('بدء نظام التحديث المستمر لمواقيت الصلاة...');

            // تحديث فوري أولي
            this.performUpdate();

            // إعداد التحديث المستمر
            this.updateInterval = setInterval(() => {
                this.performUpdate();
            }, this.updateFrequency);

            this.isRunning = true;
            this.lastUpdateTime = new Date();

            console.log(`تم بدء التحديث المستمر - سيتم التحديث كل ${this.updateFrequency / 1000} ثانية`);

            // إطلاق حدث بدء التحديث
            this.dispatchEvent('continuousUpdateStarted');

            return true;
        } catch (error) {
            console.error('خطأ في بدء التحديث المستمر:', error);
            return false;
        }
    },

    // إيقاف التحديث المستمر
    stop: function() {
        try {
            if (!this.isRunning) {
                console.log('نظام التحديث المستمر متوقف بالفعل');
                return false;
            }

            console.log('إيقاف نظام التحديث المستمر...');

            // إلغاء المؤقت
            if (this.updateInterval) {
                clearInterval(this.updateInterval);
                this.updateInterval = null;
            }

            this.isRunning = false;

            console.log('تم إيقاف التحديث المستمر');

            // إطلاق حدث إيقاف التحديث
            this.dispatchEvent('continuousUpdateStopped');

            return true;
        } catch (error) {
            console.error('خطأ في إيقاف التحديث المستمر:', error);
            return false;
        }
    },

    // تنفيذ التحديث
    performUpdate: function() {
        try {
            const currentTime = new Date();

            if (this.settings.logUpdates) {
                console.log(`تنفيذ التحديث المستمر - ${currentTime.toLocaleTimeString('ar-SA')}`);
            }

            // الحصول على المدينة الحالية
            const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';

            // تحديث المنطقة الزمنية للساعات
            if (typeof updateCurrentTimezone === 'function') {
                updateCurrentTimezone(currentCity);
            }

            // تحديث مواقيت الصلاة
            if (typeof PrayerTimesManager !== 'undefined') {
                const updatedTimes = PrayerTimesManager.updatePrayerTimesWithAdjustments(currentCity, currentTime);

                if (updatedTimes) {
                    // تحديث العرض إذا كان مفعلاً
                    if (this.settings.updateDisplay) {
                        this.updateDisplay(updatedTimes);
                    }

                    // تحديث العد التنازلي إذا كان مفعلاً
                    if (this.settings.updateCountdown) {
                        this.updateCountdown(currentCity);
                    }

                    // تحديث الساعات
                    this.updateClocks();

                    // إطلاق حدث التحديث
                    this.dispatchEvent('prayerTimesUpdated', {
                        city: currentCity,
                        times: updatedTimes,
                        timestamp: currentTime
                    });

                    this.lastUpdateTime = currentTime;
                }
            } else {
                console.warn('PrayerTimesManager غير متوفر للتحديث المستمر');
            }

        } catch (error) {
            console.error('خطأ في تنفيذ التحديث المستمر:', error);
        }
    },

    // تحديث الساعات
    updateClocks: function() {
        try {
            // تحديث الساعة التناظرية
            if (typeof updateAnalogClock === 'function') {
                updateAnalogClock();
            }

            // تحديث الساعة الرقمية
            if (typeof updateDigitalClock === 'function') {
                updateDigitalClock();
            }

            // تحديث التاريخ
            if (typeof updateDate === 'function') {
                updateDate();
            }
        } catch (error) {
            console.error('خطأ في تحديث الساعات:', error);
        }
    },

    // تحديث العرض
    updateDisplay: function(times) {
        try {
            // تحديث عرض مواقيت الصلاة
            if (typeof window.updatePrayerTimes === 'function') {
                window.updatePrayerTimes();
            } else if (typeof window.updateAllPrayerTimesDisplays === 'function') {
                window.updateAllPrayerTimesDisplays();
            } else {
                // تحديث مباشر للعناصر
                this.updatePrayerTimeElements(times);
            }
        } catch (error) {
            console.error('خطأ في تحديث العرض:', error);
        }
    },

    // تحديث عناصر مواقيت الصلاة مباشرة
    updatePrayerTimeElements: function(times) {
        try {
            const prayers = ['fajr', 'sunrise', 'dhuhr', 'asr', 'maghrib', 'isha'];
            
            prayers.forEach(prayer => {
                // البحث عن عناصر العرض المختلفة
                const selectors = [
                    `#${prayer}-time`,
                    `#${prayer}-time-display`,
                    `.${prayer}-time`,
                    `[data-prayer="${prayer}"]`
                ];

                selectors.forEach(selector => {
                    const element = document.querySelector(selector);
                    if (element && times[prayer]) {
                        element.textContent = times[prayer];
                    }
                });
            });
        } catch (error) {
            console.error('خطأ في تحديث عناصر مواقيت الصلاة:', error);
        }
    },

    // تحديث العد التنازلي
    updateCountdown: function(cityKey) {
        try {
            if (typeof PrayerTimesManager !== 'undefined') {
                const nextPrayer = PrayerTimesManager.getNextPrayer(cityKey);
                
                if (nextPrayer) {
                    // تحديث العد التنازلي
                    if (typeof window.updateCountdown === 'function') {
                        window.updateCountdown();
                    }
                    
                    // تحديث عرض الصلاة القادمة
                    if (typeof window.updateNextPrayerDisplay === 'function') {
                        window.updateNextPrayerDisplay(nextPrayer);
                    }
                }
            }
        } catch (error) {
            console.error('خطأ في تحديث العد التنازلي:', error);
        }
    },

    // إعداد مستمعات الأحداث
    setupEventListeners: function() {
        try {
            // مستمع تغيير المدينة
            window.addEventListener('cityChanged', (event) => {
                console.log('تم تغيير المدينة في التحديث المستمر - تحديث فوري للمواقيت والساعات');

                // تحديث المنطقة الزمنية
                if (typeof updateCurrentTimezone === 'function' && event.detail && event.detail.city) {
                    updateCurrentTimezone(event.detail.city);
                }

                // تحديث فوري
                if (this.isRunning) {
                    this.performUpdate();
                } else {
                    // حتى لو لم يكن التحديث المستمر يعمل، قم بتحديث الساعات
                    this.updateClocks();
                }
            });

            // مستمع تغيير المنطقة الزمنية
            window.addEventListener('timezoneChanged', (event) => {
                if (this.isRunning) {
                    console.log('تم تغيير المنطقة الزمنية - تحديث فوري للساعات والمواقيت');

                    // تحديث الساعات فوراً
                    this.updateClocks();

                    // تحديث مواقيت الصلاة
                    this.performUpdate();
                }
            });

            // مستمع تغيير الإعدادات
            window.addEventListener('settingsChanged', () => {
                if (this.isRunning) {
                    console.log('تم تغيير الإعدادات - تحديث فوري للمواقيت');
                    this.performUpdate();
                }
            });

            // مستمع إخفاء/إظهار الصفحة
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    console.log('الصفحة مخفية - الاستمرار في التحديث في الخلفية');
                } else {
                    console.log('الصفحة ظاهرة - تحديث فوري للمواقيت والساعات');
                    if (this.isRunning) {
                        this.performUpdate();
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في إعداد مستمعات الأحداث:', error);
        }
    },

    // إطلاق حدث مخصص
    dispatchEvent: function(eventName, detail = null) {
        try {
            if (typeof window !== 'undefined' && window.dispatchEvent) {
                const event = new CustomEvent(eventName, { detail });
                window.dispatchEvent(event);
            }
        } catch (error) {
            console.error(`خطأ في إطلاق الحدث ${eventName}:`, error);
        }
    },

    // تفعيل/تعطيل التحديث المستمر
    toggle: function(enabled) {
        try {
            this.settings.enabled = enabled;
            this.saveSettings();

            if (enabled) {
                return this.start();
            } else {
                return this.stop();
            }
        } catch (error) {
            console.error('خطأ في تفعيل/تعطيل التحديث المستمر:', error);
            return false;
        }
    },

    // تعيين تكرار التحديث
    setUpdateFrequency: function(milliseconds) {
        try {
            if (milliseconds < 1000) {
                console.warn('تكرار التحديث لا يمكن أن يكون أقل من ثانية واحدة');
                return false;
            }

            this.updateFrequency = milliseconds;
            
            // إعادة تشغيل التحديث بالتكرار الجديد
            if (this.isRunning) {
                this.stop();
                this.start();
            }

            console.log(`تم تعيين تكرار التحديث إلى ${milliseconds / 1000} ثانية`);
            return true;
        } catch (error) {
            console.error('خطأ في تعيين تكرار التحديث:', error);
            return false;
        }
    },

    // حفظ الإعدادات
    saveSettings: function() {
        try {
            localStorage.setItem('continuousUpdaterSettings', JSON.stringify(this.settings));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ إعدادات التحديث المستمر:', error);
            return false;
        }
    },

    // تحميل الإعدادات
    loadSettings: function() {
        try {
            const saved = localStorage.getItem('continuousUpdaterSettings');
            if (saved) {
                const parsedSettings = JSON.parse(saved);
                this.settings = { ...this.settings, ...parsedSettings };
                console.log('تم تحميل إعدادات التحديث المستمر:', this.settings);
            }
            return true;
        } catch (error) {
            console.error('خطأ في تحميل إعدادات التحديث المستمر:', error);
            return false;
        }
    },

    // الحصول على حالة النظام
    getStatus: function() {
        return {
            isRunning: this.isRunning,
            lastUpdateTime: this.lastUpdateTime,
            updateFrequency: this.updateFrequency,
            settings: this.settings
        };
    }
};

// تصدير الكائن
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PrayerTimesContinuousUpdater;
}

// تهيئة تلقائية عند تحميل الصفحة
if (typeof document !== 'undefined') {
    document.addEventListener('DOMContentLoaded', function() {
        // تأخير قصير للتأكد من تحميل جميع المكتبات
        setTimeout(() => {
            PrayerTimesContinuousUpdater.initialize();
        }, 1000);
    });
}
