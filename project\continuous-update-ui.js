/**
 * واجهة المستخدم لإدارة التحديث المستمر لمواقيت الصلاة
 * نسخة 1.0
 */

const ContinuousUpdateUI = {
    // عناصر واجهة المستخدم
    elements: {
        enableCheckbox: null,
        frequencySelect: null,
        logUpdatesCheckbox: null,
        updateDisplayCheckbox: null,
        updateCountdownCheckbox: null,
        saveButton: null,
        testButton: null,
        stopButton: null,
        statusText: null,
        lastUpdateTime: null,
        timezoneDisplay: null
    },

    // تهيئة واجهة المستخدم
    initialize: function() {
        console.log('تهيئة واجهة المستخدم للتحديث المستمر...');
        
        // الحصول على عناصر واجهة المستخدم
        this.getElements();
        
        // إعداد مستمعات الأحداث
        this.setupEventListeners();
        
        // تحميل الإعدادات المحفوظة
        this.loadSettings();
        
        // تحديث حالة واجهة المستخدم
        this.updateUI();
        
        console.log('تم تهيئة واجهة المستخدم للتحديث المستمر بنجاح');
    },

    // الحصول على عناصر واجهة المستخدم
    getElements: function() {
        this.elements.enableCheckbox = document.getElementById('enable-continuous-update');
        this.elements.frequencySelect = document.getElementById('update-frequency');
        this.elements.logUpdatesCheckbox = document.getElementById('log-updates');
        this.elements.updateDisplayCheckbox = document.getElementById('update-display');
        this.elements.updateCountdownCheckbox = document.getElementById('update-countdown');
        this.elements.saveButton = document.getElementById('save-continuous-update-settings');
        this.elements.testButton = document.getElementById('test-continuous-update');
        this.elements.stopButton = document.getElementById('stop-continuous-update');
        this.elements.statusText = document.getElementById('update-status-text');
        this.elements.lastUpdateTime = document.getElementById('last-update-time');
        this.elements.timezoneDisplay = document.getElementById('current-timezone-display');
    },

    // إعداد مستمعات الأحداث
    setupEventListeners: function() {
        // مستمع تفعيل/تعطيل التحديث المستمر
        if (this.elements.enableCheckbox) {
            this.elements.enableCheckbox.addEventListener('change', (e) => {
                this.toggleContinuousUpdate(e.target.checked);
            });
        }

        // مستمع تغيير تكرار التحديث
        if (this.elements.frequencySelect) {
            this.elements.frequencySelect.addEventListener('change', (e) => {
                this.updateFrequency(parseInt(e.target.value) * 1000);
            });
        }

        // مستمع حفظ الإعدادات
        if (this.elements.saveButton) {
            this.elements.saveButton.addEventListener('click', () => {
                this.saveSettings();
            });
        }

        // مستمع اختبار التحديث المستمر
        if (this.elements.testButton) {
            this.elements.testButton.addEventListener('click', () => {
                this.testContinuousUpdate();
            });
        }

        // مستمع إيقاف التحديث المستمر
        if (this.elements.stopButton) {
            this.elements.stopButton.addEventListener('click', () => {
                this.stopContinuousUpdate();
            });
        }

        // مستمعات أحداث التحديث المستمر
        window.addEventListener('continuousUpdateStarted', () => {
            this.updateStatus('يعمل', 'green');
        });

        window.addEventListener('continuousUpdateStopped', () => {
            this.updateStatus('متوقف', 'red');
        });

        window.addEventListener('prayerTimesUpdated', (event) => {
            if (event.detail && event.detail.timestamp) {
                this.updateLastUpdateTime(event.detail.timestamp);
            }
        });

        // مستمع تغيير المنطقة الزمنية
        window.addEventListener('timezoneChanged', (event) => {
            if (event.detail && event.detail.timezone) {
                this.updateTimezoneDisplay(event.detail.timezone);
            }
        });

        // مستمع تغيير المدينة
        window.addEventListener('cityChanged', (event) => {
            if (event.detail && event.detail.timezone) {
                this.updateTimezoneDisplay(event.detail.timezone);
            }
        });
    },

    // تفعيل/تعطيل التحديث المستمر
    toggleContinuousUpdate: function(enabled) {
        try {
            if (typeof PrayerTimesContinuousUpdater !== 'undefined') {
                const result = PrayerTimesContinuousUpdater.toggle(enabled);
                
                if (result) {
                    this.showNotification(
                        `تم ${enabled ? 'تفعيل' : 'تعطيل'} التحديث المستمر بنجاح`,
                        'success'
                    );
                    this.updateStatus(enabled ? 'يعمل' : 'متوقف', enabled ? 'green' : 'red');
                } else {
                    this.showNotification('فشل في تغيير حالة التحديث المستمر', 'error');
                }
            } else {
                this.showNotification('نظام التحديث المستمر غير متوفر', 'error');
            }
        } catch (error) {
            console.error('خطأ في تفعيل/تعطيل التحديث المستمر:', error);
            this.showNotification('حدث خطأ في تغيير حالة التحديث المستمر', 'error');
        }
    },

    // تحديث تكرار التحديث
    updateFrequency: function(milliseconds) {
        try {
            if (typeof PrayerTimesContinuousUpdater !== 'undefined') {
                const result = PrayerTimesContinuousUpdater.setUpdateFrequency(milliseconds);
                
                if (result) {
                    this.showNotification(
                        `تم تعيين تكرار التحديث إلى ${milliseconds / 1000} ثانية`,
                        'success'
                    );
                } else {
                    this.showNotification('فشل في تعيين تكرار التحديث', 'error');
                }
            }
        } catch (error) {
            console.error('خطأ في تحديث تكرار التحديث:', error);
            this.showNotification('حدث خطأ في تعيين تكرار التحديث', 'error');
        }
    },

    // اختبار التحديث المستمر
    testContinuousUpdate: function() {
        try {
            if (typeof PrayerTimesContinuousUpdater !== 'undefined') {
                // تنفيذ تحديث فوري
                PrayerTimesContinuousUpdater.performUpdate();
                this.showNotification('تم تنفيذ تحديث فوري للمواقيت', 'info');
            } else {
                this.showNotification('نظام التحديث المستمر غير متوفر', 'error');
            }
        } catch (error) {
            console.error('خطأ في اختبار التحديث المستمر:', error);
            this.showNotification('حدث خطأ في اختبار التحديث المستمر', 'error');
        }
    },

    // إيقاف التحديث المستمر
    stopContinuousUpdate: function() {
        try {
            if (typeof PrayerTimesContinuousUpdater !== 'undefined') {
                const result = PrayerTimesContinuousUpdater.stop();
                
                if (result) {
                    this.showNotification('تم إيقاف التحديث المستمر', 'info');
                    this.elements.enableCheckbox.checked = false;
                    this.updateStatus('متوقف', 'red');
                } else {
                    this.showNotification('التحديث المستمر متوقف بالفعل', 'warning');
                }
            }
        } catch (error) {
            console.error('خطأ في إيقاف التحديث المستمر:', error);
            this.showNotification('حدث خطأ في إيقاف التحديث المستمر', 'error');
        }
    },

    // حفظ الإعدادات
    saveSettings: function() {
        try {
            const settings = {
                enabled: this.elements.enableCheckbox?.checked || false,
                frequency: parseInt(this.elements.frequencySelect?.value || 60),
                logUpdates: this.elements.logUpdatesCheckbox?.checked || false,
                updateDisplay: this.elements.updateDisplayCheckbox?.checked || true,
                updateCountdown: this.elements.updateCountdownCheckbox?.checked || true
            };

            // حفظ في التخزين المحلي
            localStorage.setItem('continuousUpdateUISettings', JSON.stringify(settings));

            // تطبيق الإعدادات على نظام التحديث المستمر
            if (typeof PrayerTimesContinuousUpdater !== 'undefined') {
                PrayerTimesContinuousUpdater.settings.logUpdates = settings.logUpdates;
                PrayerTimesContinuousUpdater.settings.updateDisplay = settings.updateDisplay;
                PrayerTimesContinuousUpdater.settings.updateCountdown = settings.updateCountdown;
                PrayerTimesContinuousUpdater.saveSettings();
            }

            this.showNotification('تم حفظ إعدادات التحديث المستمر بنجاح', 'success');
        } catch (error) {
            console.error('خطأ في حفظ إعدادات التحديث المستمر:', error);
            this.showNotification('حدث خطأ في حفظ الإعدادات', 'error');
        }
    },

    // تحميل الإعدادات
    loadSettings: function() {
        try {
            const saved = localStorage.getItem('continuousUpdateUISettings');
            if (saved) {
                const settings = JSON.parse(saved);
                
                if (this.elements.enableCheckbox) {
                    this.elements.enableCheckbox.checked = settings.enabled || false;
                }
                
                if (this.elements.frequencySelect) {
                    this.elements.frequencySelect.value = settings.frequency || 60;
                }
                
                if (this.elements.logUpdatesCheckbox) {
                    this.elements.logUpdatesCheckbox.checked = settings.logUpdates !== false;
                }
                
                if (this.elements.updateDisplayCheckbox) {
                    this.elements.updateDisplayCheckbox.checked = settings.updateDisplay !== false;
                }
                
                if (this.elements.updateCountdownCheckbox) {
                    this.elements.updateCountdownCheckbox.checked = settings.updateCountdown !== false;
                }
            }
        } catch (error) {
            console.error('خطأ في تحميل إعدادات التحديث المستمر:', error);
        }
    },

    // تحديث واجهة المستخدم
    updateUI: function() {
        try {
            if (typeof PrayerTimesContinuousUpdater !== 'undefined') {
                const status = PrayerTimesContinuousUpdater.getStatus();
                
                // تحديث حالة التشغيل
                this.updateStatus(
                    status.isRunning ? 'يعمل' : 'متوقف',
                    status.isRunning ? 'green' : 'red'
                );
                
                // تحديث آخر وقت تحديث
                if (status.lastUpdateTime) {
                    this.updateLastUpdateTime(status.lastUpdateTime);
                }
                
                // تحديث تكرار التحديث
                if (this.elements.frequencySelect) {
                    this.elements.frequencySelect.value = status.updateFrequency / 1000;
                }
            }
        } catch (error) {
            console.error('خطأ في تحديث واجهة المستخدم:', error);
        }
    },

    // تحديث حالة النظام
    updateStatus: function(status, color) {
        if (this.elements.statusText) {
            this.elements.statusText.textContent = `حالة التحديث: ${status}`;
            this.elements.statusText.style.color = color;
        }
    },

    // تحديث آخر وقت تحديث
    updateLastUpdateTime: function(timestamp) {
        if (this.elements.lastUpdateTime) {
            const timeString = new Date(timestamp).toLocaleTimeString('ar-SA');
            this.elements.lastUpdateTime.textContent = `آخر تحديث: ${timeString}`;
        }
    },

    // تحديث عرض المنطقة الزمنية
    updateTimezoneDisplay: function(timezone) {
        if (this.elements.timezoneDisplay) {
            this.elements.timezoneDisplay.textContent = `المنطقة الزمنية: ${timezone}`;
        }
    },

    // عرض إشعار
    showNotification: function(message, type = 'info') {
        try {
            // استخدام نظام الإشعارات الموجود إذا كان متوفراً
            if (typeof showNotification === 'function') {
                showNotification(message, type);
            } else {
                // عرض إشعار بسيط
                console.log(`[${type.toUpperCase()}] ${message}`);
                alert(message);
            }
        } catch (error) {
            console.error('خطأ في عرض الإشعار:', error);
        }
    }
};

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تأخير قصير للتأكد من تحميل جميع العناصر
    setTimeout(() => {
        ContinuousUpdateUI.initialize();
    }, 1500);
});

// تصدير الكائن
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ContinuousUpdateUI;
}
